import { generateText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { Tweet } from '@/lib/twitter';

export type SummaryResult = {
  summary: string;
  key_points: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  participants: string[];
};

export class AISummarizer {
  private model;
  private client;

  constructor() {
    // Initialize OpenRouter client using OpenAI-compatible interface
    this.client = createOpenAI({
      apiKey: process.env.OPENROUTER_API_KEY!,
      baseURL: 'https://openrouter.ai/api/v1',
    });
    
    // Get model from environment variable with fallback
    const modelName = process.env.AI_MODEL || 'google/gemini-2.0-flash-exp';
    this.model = this.client(modelName);
    
    console.log(`[AISummarizer] Using OpenRouter model: ${modelName}`);
  }

  async summarizeContext(tweets: Tweet[]): Promise<SummaryResult> {
    if (tweets.length === 0) {
      throw new Error('No tweets provided for summarization');
    }

    // Sort tweets by creation date to maintain chronological order
    const sortedTweets = [...tweets].sort((a, b) => 
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    // Format tweets for the AI prompt
    const tweetContext = sortedTweets.map((tweet, index) => {
      const metrics = tweet.public_metrics;
      return `${index + 1}. @${tweet.author.username} (${tweet.author.name}):
"${tweet.text}"
[${metrics.like_count} likes, ${metrics.retweet_count} retweets, ${metrics.reply_count} replies]
Posted: ${new Date(tweet.created_at).toLocaleString()}`;
    }).join('\n\n');

    // Determine if this is a single tweet or a thread
    const isThread = tweets.length > 1;
    const threadType = this.determineThreadType(tweets);
    
    const prompt = `You are analyzing a Twitter/X ${isThread ? 'thread' : 'tweet'}. Please provide a concise summary of the following content:

${tweetContext}

Context: This is ${threadType}.

Please analyze this content and provide:
1. A clear, concise summary of what this ${isThread ? 'thread' : 'tweet'} is about
2. Key points or insights ${isThread ? 'from the thread progression' : 'from the tweet'}
3. Overall sentiment (positive, negative, or neutral)
4. Key participants involved

${isThread ? 
  'Focus on how the thread develops, what story it tells, and the main narrative from start to finish.' : 
  'Focus on the main message, any context references, and what the tweet is responding to if applicable.'
}

Keep the summary concise but informative - someone should understand the essence without reading the original ${isThread ? 'thread' : 'tweet'}.`;

    try {
      const result = await generateText({
        model: this.model,
        prompt,
        maxTokens: 500,
        temperature: 0.3,
      });

      // Parse the result - since we're not using structured output, we'll extract info manually
      const summary = result.text;
      
      // Extract key information from the summary
      const keyPoints = this.extractKeyPoints(summary);
      const sentiment = this.detectSentiment(summary);
      const participants = this.extractParticipants(tweets);

      return {
        summary: summary.trim(),
        key_points: keyPoints,
        sentiment,
        participants
      };
    } catch (error) {
      console.error('AI summarization error:', error);
      throw new Error('Failed to generate summary');
    }
  }

  private extractKeyPoints(text: string): string[] {
    // Simple extraction of bullet points or numbered lists
    const lines = text.split('\n');
    const keyPoints: string[] = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.match(/^[\-\*\d\.]/)) {
        keyPoints.push(trimmed.replace(/^[\-\*\d\.\s]+/, ''));
      }
    }
    
    return keyPoints.slice(0, 5); // Limit to top 5 points
  }

  private detectSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['good', 'great', 'excellent', 'positive', 'success', 'happy', 'love', 'amazing'];
    const negativeWords = ['bad', 'terrible', 'negative', 'failure', 'sad', 'hate', 'awful', 'worse'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private extractParticipants(tweets: Tweet[]): string[] {
    const participants = new Set<string>();
    
    for (const tweet of tweets) {
      participants.add(`@${tweet.author.username}`);
    }
    
    return Array.from(participants).slice(0, 5); // Limit to top 5 participants
  }

  private determineThreadType(tweets: Tweet[]): string {
    if (tweets.length === 1) {
      const tweet = tweets[0];
      if (tweet.referenced_tweets?.some(ref => ref.type === 'replied_to')) {
        return 'a single tweet that is part of a conversation or reply to another tweet';
      }
      return 'a standalone tweet';
    }

    // Check if all tweets are from the same author (thread)
    const authors = new Set(tweets.map(t => t.author.id));
    if (authors.size === 1) {
      return `a ${tweets.length}-tweet thread by @${tweets[0].author.username}`;
    }

    // Mixed authors - conversation
    return `a ${tweets.length}-tweet conversation between ${authors.size} participants`;
  }

  async generateReplyText(originalTweet: Tweet, summary: SummaryResult, tweets: Tweet[]): Promise<string> {
    const isThread = tweets.length > 1;
    const authors = new Set(tweets.map(t => t.author.id));
    const isThreadByAuthor = authors.size === 1 && isThread;

    let replyText = `🧵 **${isThreadByAuthor ? 'Thread' : isThread ? 'Conversation' : 'Tweet'} Summary**\n\n`;

    // Add thread/conversation info
    if (isThread) {
      replyText += `📊 **${isThreadByAuthor ? 'Thread' : 'Conversation'} Info:**\n`;
      replyText += `• ${tweets.length} tweets total\n`;
      if (!isThreadByAuthor) {
        replyText += `• ${authors.size} participants\n`;
      }
      replyText += `• Author: @${originalTweet.author.username}\n\n`;
    }

    // Add the main tweet
    replyText += `📝 **Main Tweet:**\n${originalTweet.text}\n\n`;

    // Add context summary
    replyText += `🔍 **Context:**\n${summary.summary}\n\n`;

    // Add key points if available
    if (summary.key_points.length > 0) {
      replyText += `💡 **Key Points:**\n${summary.key_points.map(point => `• ${point}`).join('\n')}\n\n`;
    }

    // Add participants and sentiment
    if (isThread && !isThreadByAuthor) {
      replyText += `👥 **Participants:** ${summary.participants.join(', ')}\n`;
    }
    
    const sentimentEmoji = summary.sentiment === 'positive' ? '😊' : summary.sentiment === 'negative' ? '😔' : '😐';
    replyText += `📊 **Tone:** ${sentimentEmoji} ${summary.sentiment.charAt(0).toUpperCase() + summary.sentiment.slice(1)}\n\n`;

    replyText += `_🤖 AI-generated summary • Thread context focused_`;

    return replyText;
  }
}

export const aiSummarizer = new AISummarizer();