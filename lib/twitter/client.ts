import { createClient } from "@/lib/supabase/server";

export interface Tweet {
  id: string;
  text: string;
  author: {
    id: string;
    username: string;
    name: string;
  };
  created_at: string;
  public_metrics: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
  };
  referenced_tweets?: Array<{
    type: 'replied_to' | 'quoted' | 'retweeted';
    id: string;
  }>;
  in_reply_to_user_id?: string;
}

interface RawTweet {
  id: string;
  text: string;
  author_id: string;
  created_at: string;
  public_metrics: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
  };
  referenced_tweets?: Array<{
    type: 'replied_to' | 'quoted' | 'retweeted';
    id: string;
  }>;
  in_reply_to_user_id?: string;
}

export interface TwitterApiResponse {
  data: RawTweet[];
  includes?: {
    users?: Array<{
      id: string;
      username: string;
      name: string;
    }>;
    tweets?: RawTweet[];
  };
}

class TwitterApiError extends <PERSON>rror {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'TwitterApiError';
  }
}

export class TwitterClient {
  private apiKey: string;
  private baseUrl = 'https://api.twitter.com/2';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async fetchTweets(tweetIds: string[]): Promise<Tweet[]> {
    const params = new URLSearchParams({
      ids: tweetIds.join(','),
      'tweet.fields': 'id,text,author_id,created_at,public_metrics,referenced_tweets,in_reply_to_user_id',
      'user.fields': 'id,username,name',
      'expansions': 'author_id,referenced_tweets.id,in_reply_to_user_id'
    });

    const response = await fetch(`${this.baseUrl}/tweets?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new TwitterApiError(`Twitter API error: ${response.status}`, response.status);
    }

    const data: TwitterApiResponse = await response.json();
    
    if (!data.data) {
      throw new TwitterApiError('No tweet data returned');
    }

    // Merge user data with tweets
    const tweets = data.data.map(tweet => ({
      ...tweet,
      author: data.includes?.users?.find(user => user.id === tweet.author_id) || {
        id: tweet.author_id,
        username: 'unknown',
        name: 'Unknown User'
      }
    }));

    return tweets;
  }

  async fetchTweetContext(tweetId: string, depth = 10): Promise<Tweet[]> {
    const threadTweets: Tweet[] = [];
    const processedIds = new Set<string>();

    // First, get the main tweet
    try {
      const [mainTweet] = await this.fetchTweets([tweetId]);
      if (!mainTweet) {
        return [];
      }
      
      threadTweets.push(mainTweet);
      processedIds.add(tweetId);

      // Traverse up to find the thread start (parent tweets)
      await this.fetchThreadParents(mainTweet, threadTweets, processedIds, depth);
      
      // Traverse down to find the thread continuation (child tweets)
      await this.fetchThreadChildren(tweetId);

      // Sort tweets chronologically (oldest first)
      threadTweets.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

    } catch (error) {
      console.warn(`Failed to fetch tweet context for ${tweetId}:`, error);
    }

    return threadTweets;
  }

  private async fetchThreadParents(tweet: Tweet, threadTweets: Tweet[], processedIds: Set<string>, maxDepth: number): Promise<void> {
    let currentTweet = tweet;
    let depth = 0;

    while (depth < maxDepth) {
      // Check if this tweet is a reply to another tweet
      const replyRef = currentTweet.referenced_tweets?.find(ref => ref.type === 'replied_to');
      if (!replyRef || processedIds.has(replyRef.id)) {
        break;
      }

      try {
        const [parentTweet] = await this.fetchTweets([replyRef.id]);
        if (!parentTweet) break;

        // Only include if it's from the same author (thread continuation)
        if (parentTweet.author.id === tweet.author.id) {
          threadTweets.push(parentTweet);
          processedIds.add(parentTweet.id);
          currentTweet = parentTweet;
          depth++;
        } else {
          // If parent is from different author, we've reached the end of the thread
          // But still include this one parent for context
          threadTweets.push(parentTweet);
          processedIds.add(parentTweet.id);
          break;
        }
      } catch (error) {
        console.warn(`Failed to fetch parent tweet ${replyRef.id}:`, error);
        break;
      }
    }
  }

  private async fetchThreadChildren(tweetId: string): Promise<void> {
    // Note: Twitter API v2 doesn't provide a direct way to get replies to a tweet
    // This would require searching for tweets that reply to this tweet
    // For now, we'll focus on the parent chain which gives us the main thread context
    
    // If you have access to Twitter API v1.1 or Academic Research access,
    // you could implement this by searching for tweets with in_reply_to_status_id
    console.log(`Thread children fetch not implemented for tweet ${tweetId} - focusing on parent chain`);
  }

  async getCachedSummary(tweetId: string): Promise<{ text: string; summary: string; context: Tweet[] } | null> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('buddyintels_tweet_summaries')
      .select('*')
      .eq('tweet_id', tweetId)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data) return null;

    return {
      text: data.tweet_text,
      summary: data.context_summary,
      context: data.context_tweets
    };
  }

  async cacheSummary(tweetId: string, tweetText: string, summary: string, context: Tweet[]): Promise<void> {
    const supabase = await createClient();
    
    await supabase
      .from('buddyintels_tweet_summaries')
      .upsert({
        tweet_id: tweetId,
        tweet_text: tweetText,
        context_summary: summary,
        context_tweets: context,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
  }
}

export const twitterClient = new TwitterClient(process.env.TWITTER_API_KEY!);