import { Context } from 'grammy';
import { createClient } from '@/lib/supabase/server';

export interface CommandUsageData {
  userId: number;
  username?: string;
  firstName?: string;
  lastName?: string;
  chatId: number;
  chatType: string;
  chatTitle?: string;
  topicId?: number;
  topicTitle?: string;
  command: string;
  commandArgs?: string;
  success: boolean;
  errorMessage?: string;
  responseTimeMs?: number;
  rateLimited: boolean;
}

export class CommandRecorder {
  private static instance: CommandRecorder;
  private recordingEnabled: boolean = true;

  private constructor() {}

  public static getInstance(): CommandRecorder {
    if (!CommandRecorder.instance) {
      CommandRecorder.instance = new CommandRecorder();
    }
    return CommandRecorder.instance;
  }

  /**
   * Enable or disable command recording
   */
  public setRecordingEnabled(enabled: boolean): void {
    this.recordingEnabled = enabled;
    console.log(`[CommandRecorder] Recording ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Record a command execution
   */
  public async recordCommand(data: CommandUsageData): Promise<void> {
    if (!this.recordingEnabled) {
      return;
    }

    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('buddyintels_command_usage')
        .insert({
          user_id: data.userId,
          username: data.username,
          first_name: data.firstName,
          last_name: data.lastName,
          chat_id: data.chatId,
          chat_type: data.chatType,
          chat_title: data.chatTitle,
          topic_id: data.topicId,
          topic_title: data.topicTitle,
          command: data.command,
          command_args: data.commandArgs,
          success: data.success,
          error_message: data.errorMessage,
          response_time_ms: data.responseTimeMs,
          rate_limited: data.rateLimited
        });

      if (error) {
        console.error('[CommandRecorder] Failed to record command:', error);
      } else {
        console.log(`[CommandRecorder] Recorded command: ${data.command} by user ${data.userId} in chat ${data.chatId}`);
      }
    } catch (error) {
      console.error('[CommandRecorder] Error recording command:', error);
    }
  }

  /**
   * Extract command usage data from Grammy context
   */
  public extractCommandData(ctx: Context, command: string, success: boolean = true, errorMessage?: string, responseTimeMs?: number, rateLimited: boolean = false): CommandUsageData {
    const message = ctx.message;
    const user = ctx.from;
    const chat = ctx.chat;

    if (!user || !chat) {
      throw new Error('Missing user or chat information in context');
    }

    // Extract command arguments (everything after the command)
    let commandArgs: string | undefined;
    if (message && 'text' in message && message.text) {
      const parts = message.text.split(' ');
      if (parts.length > 1) {
        commandArgs = parts.slice(1).join(' ');
      }
    }

    // Extract topic information if available
    let topicId: number | undefined;
    let topicTitle: string | undefined;
    
    if (message && 'message_thread_id' in message && message.message_thread_id) {
      topicId = message.message_thread_id;
      // Topic title might be available in some contexts
      if ('reply_to_message' in message && message.reply_to_message && 'forum_topic_created' in message.reply_to_message) {
        topicTitle = (message.reply_to_message as { forum_topic_created?: { name: string } }).forum_topic_created?.name;
      }
    }

    return {
      userId: user.id,
      username: user.username,
      firstName: user.first_name,
      lastName: user.last_name,
      chatId: chat.id,
      chatType: chat.type,
      chatTitle: 'title' in chat ? chat.title : undefined,
      topicId,
      topicTitle,
      command,
      commandArgs,
      success,
      errorMessage,
      responseTimeMs,
      rateLimited
    };
  }

  /**
   * Create a middleware function for Grammy that automatically records commands
   */
  public createMiddleware() {
    return async (ctx: Context, next: () => Promise<void>) => {
      const startTime = Date.now();
      let command: string | undefined;
      let success = true;
      let errorMessage: string | undefined;
      let rateLimited = false;

      try {
        // Extract command from message
        if (ctx.message && 'text' in ctx.message && ctx.message.text) {
          const text = ctx.message.text;
          if (text.startsWith('/')) {
            const commandMatch = text.match(/^\/([a-zA-Z0-9_]+)/);
            if (commandMatch) {
              command = commandMatch[1];
            }
          }
        }

        // Execute the next middleware/handler
        await next();

      } catch (error) {
        success = false;
        errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        // Check if it's a rate limit error
        if (errorMessage.includes('rate limit') || errorMessage.includes('Rate limit')) {
          rateLimited = true;
        }
        
        // Re-throw the error so it's handled by other error handlers
        throw error;
      } finally {
        // Record the command if we detected one
        if (command) {
          const responseTime = Date.now() - startTime;
          
          try {
            const commandData = this.extractCommandData(
              ctx, 
              command, 
              success, 
              errorMessage, 
              responseTime, 
              rateLimited
            );
            
            // Record asynchronously to avoid blocking the response
            this.recordCommand(commandData).catch(err => {
              console.error('[CommandRecorder] Failed to record command in middleware:', err);
            });
          } catch (extractError) {
            console.error('[CommandRecorder] Failed to extract command data:', extractError);
          }
        }
      }
    };
  }

  /**
   * Record a manual command (for cases where middleware doesn't catch it)
   */
  public async recordManualCommand(
    ctx: Context, 
    command: string, 
    success: boolean = true, 
    errorMessage?: string, 
    responseTimeMs?: number,
    rateLimited: boolean = false
  ): Promise<void> {
    try {
      const commandData = this.extractCommandData(ctx, command, success, errorMessage, responseTimeMs, rateLimited);
      await this.recordCommand(commandData);
    } catch (error) {
      console.error('[CommandRecorder] Failed to record manual command:', error);
    }
  }

  /**
   * Get command usage statistics
   */
  public async getCommandStats(days: number = 7): Promise<Record<string, unknown>[]> {
    try {
      const supabase = await createClient();
      
      const { data, error } = await supabase
        .rpc('get_command_stats', {
          start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
          end_date: new Date().toISOString()
        });

      if (error) {
        console.error('[CommandRecorder] Failed to get command stats:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('[CommandRecorder] Error getting command stats:', error);
      return [];
    }
  }

  /**
   * Get top users by activity
   */
  public async getTopUsers(limit: number = 10, days: number = 30): Promise<Record<string, unknown>[]> {
    try {
      const supabase = await createClient();
      
      const { data, error } = await supabase
        .rpc('get_top_users', {
          limit_count: limit,
          start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
        });

      if (error) {
        console.error('[CommandRecorder] Failed to get top users:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('[CommandRecorder] Error getting top users:', error);
      return [];
    }
  }

  /**
   * Clean up old command usage data
   */
  public async cleanupOldData(daysToKeep: number = 90): Promise<number> {
    try {
      const supabase = await createClient();
      
      const { data, error } = await supabase
        .rpc('cleanup_old_command_usage', {
          days_to_keep: daysToKeep
        });

      if (error) {
        console.error('[CommandRecorder] Failed to cleanup old data:', error);
        return 0;
      }

      const deletedCount = data || 0;
      console.log(`[CommandRecorder] Cleaned up ${deletedCount} old command usage records`);
      return deletedCount;
    } catch (error) {
      console.error('[CommandRecorder] Error cleaning up old data:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const commandRecorder = CommandRecorder.getInstance();

// Helper function to create timing wrapper for commands
export function withCommandTiming<T extends unknown[], R>(
  command: string,
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    let success = true;
    let errorMessage: string | undefined;

    try {
      const result = await handler(...args);
      return result;
    } catch (error) {
      success = false;
      errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    } finally {
      const responseTime = Date.now() - startTime;
      console.log(`[CommandTiming] ${command} took ${responseTime}ms, success: ${success}`);
      
      // If we have context in the first argument, record it
      if (args[0] && typeof args[0] === 'object' && 'from' in args[0] && 'chat' in args[0]) {
        const ctx = args[0] as Context;
        commandRecorder.recordManualCommand(ctx, command, success, errorMessage, responseTime)
          .catch(err => console.error('[CommandTiming] Failed to record:', err));
      }
    }
  };
}
