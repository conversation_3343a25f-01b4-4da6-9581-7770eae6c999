# 📊 Current BuddyIntels Database Schema

## Overview
This document reflects the **actual current state** of your BuddyIntels database as of 2025-07-19.

## Tables Summary

| Table Name | Purpose | Records | Status |
|------------|---------|---------|--------|
| `buddyintels_bot_config` | Bot configuration storage | 7 configs | ✅ Active |
| `buddyintels_linked_topics` | Dynamic topic management | Variable | ✅ Active |
| `buddyintels_topic_permissions` | User permissions | Variable | ✅ Active |
| `buddyintels_processed_messages` | Duplicate prevention | Variable | ✅ Active |
| `buddyintels_tweet_summaries` | Summary caching | Variable | ✅ Active |
| `buddyintels_error_logs` | Error tracking | Variable | ✅ Active |
| `buddyintels_topic_activity_logs` | Activity tracking | Variable | ✅ Active |

## Detailed Schema

### 1. `buddyintels_bot_config`
**Purpose:** Stores bot configuration settings

```sql
CREATE TABLE buddyintels_bot_config (
  id SERIAL PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Current Configuration:**
- `rate_limit_per_minute`: "5"
- `summary_cache_hours`: "24"
- `max_context_depth`: "5"
- `require_admin_for_linking`: "false"
- `auto_detect_topic_names`: "true"
- `allowed_group_id`: ""
- `allowed_topic_id`: ""

### 2. `buddyintels_linked_topics`
**Purpose:** Manages dynamically linked Telegram topics

```sql
CREATE TABLE buddyintels_linked_topics (
  id SERIAL PRIMARY KEY,
  group_id BIGINT NOT NULL,
  topic_id BIGINT,  -- NULL for general group chat
  group_title TEXT,
  topic_title TEXT,
  linked_by_user_id BIGINT NOT NULL,
  linked_by_username TEXT,
  linked_by_first_name TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(group_id, topic_id)
);
```

### 3. `buddyintels_topic_permissions`
**Purpose:** User permissions for topic management

```sql
CREATE TABLE buddyintels_topic_permissions (
  id SERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  username TEXT,
  first_name TEXT,
  is_admin BOOLEAN DEFAULT FALSE,
  can_link_topics BOOLEAN DEFAULT TRUE,
  can_manage_all_topics BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);
```

### 4. `buddyintels_processed_messages`
**Purpose:** Prevents duplicate tweet processing

```sql
CREATE TABLE buddyintels_processed_messages (
  id SERIAL PRIMARY KEY,
  message_id BIGINT NOT NULL,
  chat_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  tweet_url TEXT NOT NULL,
  processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(message_id, chat_id)
);
```

### 5. `buddyintels_tweet_summaries`
**Purpose:** Caches AI-generated tweet summaries

```sql
CREATE TABLE buddyintels_tweet_summaries (
  id SERIAL PRIMARY KEY,
  tweet_id TEXT UNIQUE NOT NULL,
  tweet_text TEXT NOT NULL,
  context_summary TEXT NOT NULL,
  context_tweets JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours')
);
```

### 6. `buddyintels_error_logs`
**Purpose:** Comprehensive error tracking and debugging

```sql
CREATE TABLE buddyintels_error_logs (
  id SERIAL PRIMARY KEY,
  error_type TEXT NOT NULL,
  error_message TEXT NOT NULL,
  stack_trace TEXT,
  context JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 7. `buddyintels_topic_activity_logs`
**Purpose:** Tracks all topic management activities

```sql
CREATE TABLE buddyintels_topic_activity_logs (
  id SERIAL PRIMARY KEY,
  topic_id INTEGER REFERENCES buddyintels_linked_topics(id),
  user_id BIGINT NOT NULL,
  username TEXT,
  action TEXT NOT NULL, -- 'linked', 'unlinked', 'activated', 'deactivated', 'updated'
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Indexes

### Performance Indexes
```sql
-- Linked Topics
CREATE INDEX idx_linked_topics_group_id ON buddyintels_linked_topics(group_id);
CREATE INDEX idx_linked_topics_topic_id ON buddyintels_linked_topics(topic_id);
CREATE INDEX idx_linked_topics_is_active ON buddyintels_linked_topics(is_active);
CREATE INDEX idx_linked_topics_linked_by_user_id ON buddyintels_linked_topics(linked_by_user_id);

-- Topic Permissions
CREATE INDEX idx_topic_permissions_user_id ON buddyintels_topic_permissions(user_id);

-- Processed Messages
CREATE INDEX idx_processed_messages_chat_id ON buddyintels_processed_messages(chat_id);
CREATE INDEX idx_processed_messages_user_id ON buddyintels_processed_messages(user_id);

-- Tweet Summaries
CREATE INDEX idx_tweet_summaries_tweet_id ON buddyintels_tweet_summaries(tweet_id);
CREATE INDEX idx_tweet_summaries_expires_at ON buddyintels_tweet_summaries(expires_at);

-- Error Logs
CREATE INDEX idx_error_logs_created_at ON buddyintels_error_logs(created_at);

-- Activity Logs
CREATE INDEX idx_topic_activity_logs_topic_id ON buddyintels_topic_activity_logs(topic_id);
CREATE INDEX idx_topic_activity_logs_user_id ON buddyintels_topic_activity_logs(user_id);
CREATE INDEX idx_topic_activity_logs_created_at ON buddyintels_topic_activity_logs(created_at);
```

## Foreign Key Constraints

```sql
-- Topic Activity Logs references Linked Topics
ALTER TABLE buddyintels_topic_activity_logs 
ADD CONSTRAINT buddyintels_topic_activity_logs_topic_id_fkey 
FOREIGN KEY (topic_id) REFERENCES buddyintels_linked_topics(id);
```

## Row Level Security (RLS)

All tables have RLS enabled with appropriate policies for:
- Service role: Full access
- Authenticated users: Read access + specific write permissions
- User-specific data isolation where applicable

## Key Changes Made

1. ✅ **Fixed table name case sensitivity** - All tables now use lowercase names
2. ✅ **Added missing config entries** - `allowed_group_id` and `allowed_topic_id`
3. ✅ **Updated all SQL files** to match actual database structure
4. ✅ **Verified all indexes and constraints** are properly defined
5. ✅ **Updated RLS policies** to use correct table names

## Files Updated

- ✅ `supabase/schema.sql` - Complete schema with correct table names
- ✅ `supabase/minimal-setup.sql` - Minimal setup with correct names
- ✅ `supabase/rls_policies.sql` - RLS policies with correct table names
- ✅ All TypeScript files with database queries
- ✅ README.md documentation

## Status

🎉 **All database references are now consistent!** The `/link` feature should work correctly now that all code references match the actual database table names.
