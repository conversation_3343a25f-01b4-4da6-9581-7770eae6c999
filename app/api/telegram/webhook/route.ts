import { NextRequest, NextResponse } from 'next/server';
// import { webhookCallback } from 'grammy';
import { createTelegramBot } from '@/lib/telegram';
import { createClient } from '@/lib/supabase/server';

// Create bot instance
let botInstance: ReturnType<typeof createTelegramBot> | null = null;
let botInitialized = false;

async function getBot() {
  if (!botInstance) {
    botInstance = createTelegramBot();
  }
  
  if (!botInitialized) {
    await botInstance.getBot().init();
    botInitialized = true;
  }
  
  return botInstance;
}

// Webhook handler (keeping for potential future use)
// const handleWebhook = webhookCallback(getBot().getBot(), 'next-js');

export async function POST(request: NextRequest) {
  try {
    // Verify webhook secret if configured
    const webhookSecret = process.env.TELEGRAM_WEBHOOK_SECRET;
    if (webhookSecret) {
      const providedSecret = request.headers.get('x-telegram-bot-api-secret-token');
      if (providedSecret !== webhookSecret) {
        console.warn('Invalid webhook secret provided');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
    }

    // Parse the request body
    const body = await request.text();
    const update = JSON.parse(body);

    // Process the update directly with the bot
    const bot = await getBot();
    await bot.getBot().handleUpdate(update);

    // Return success response
    return NextResponse.json({ ok: true });
    
  } catch (error) {
    console.error('Webhook error:', error);
    
    // Log error to database
    try {
      const supabase = await createClient();
      await supabase
        .from('buddyintels_error_logs')
        .insert({
          error_type: 'webhook_error',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          stack_trace: error instanceof Error ? error.stack : undefined,
          context: {
            url: request.url,
            headers: Object.fromEntries(request.headers.entries()),
            timestamp: new Date().toISOString()
          }
        });
    } catch (dbError) {
      console.error('Failed to log error to database:', dbError);
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Health check endpoint
export async function GET() {
  try {
    // Basic health check
    const bot = await getBot();
    const botInfo = await bot.getBot().api.getMe();
    
    return NextResponse.json({
      status: 'healthy',
      bot: {
        id: botInfo.id,
        username: botInfo.username,
        first_name: botInfo.first_name
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { 
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}