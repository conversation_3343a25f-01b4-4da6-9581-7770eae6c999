-- Telegram <PERSON>t Database Schema
-- Run this in your Supabase SQL Editor

-- Bot configuration table
CREATE TABLE IF NOT EXISTS BuddyIntels_bot_config (
  id SERIAL PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Linked topics for dynamic topic management
CREATE TABLE IF NOT EXISTS BuddyIntels_linked_topics (
  id SERIAL PRIMARY KEY,
  group_id BIGINT NOT NULL,
  topic_id BIGINT,  -- NULL for general group chat (no topics)
  group_title TEXT,
  topic_title TEXT,
  linked_by_user_id BIGINT NOT NULL,
  linked_by_username TEXT,
  linked_by_first_name TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure unique combination of group_id and topic_id
  UNIQUE(group_id, topic_id)
);

-- User permissions for topic management
CREATE TABLE IF NOT EXISTS BuddyIntels_topic_permissions (
  id SERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  username TEXT,
  first_name TEXT,
  is_admin BOOLEAN DEFAULT FALSE,
  can_link_topics BOOLEAN DEFAULT TRUE,
  can_manage_all_topics BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  UNIQUE(user_id)
);

-- Processed messages to prevent duplicate processing
CREATE TABLE IF NOT EXISTS BuddyIntels_processed_messages (
  id SERIAL PRIMARY KEY,
  message_id BIGINT NOT NULL,
  chat_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  tweet_url TEXT NOT NULL,
  processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  UNIQUE(message_id, chat_id)
);

-- Tweet summaries cache
CREATE TABLE IF NOT EXISTS BuddyIntels_tweet_summaries (
  id SERIAL PRIMARY KEY,
  tweet_id TEXT UNIQUE NOT NULL,
  tweet_text TEXT NOT NULL,
  context_summary TEXT NOT NULL,
  context_tweets JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '24 hours'
);

-- Error logs for debugging
CREATE TABLE IF NOT EXISTS BuddyIntels_error_logs (
  id SERIAL PRIMARY KEY,
  error_type TEXT NOT NULL,
  error_message TEXT NOT NULL,
  stack_trace TEXT,
  context JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Topic management activity logs
CREATE TABLE IF NOT EXISTS BuddyIntels_topic_activity_logs (
  id SERIAL PRIMARY KEY,
  topic_id INTEGER REFERENCES BuddyIntels_linked_topics(id),
  user_id BIGINT NOT NULL,
  username TEXT,
  action TEXT NOT NULL, -- 'linked', 'unlinked', 'activated', 'deactivated', 'updated'
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_linked_topics_group_id ON BuddyIntels_linked_topics(group_id);
CREATE INDEX IF NOT EXISTS idx_linked_topics_topic_id ON BuddyIntels_linked_topics(topic_id);
CREATE INDEX IF NOT EXISTS idx_linked_topics_is_active ON BuddyIntels_linked_topics(is_active);
CREATE INDEX IF NOT EXISTS idx_linked_topics_linked_by_user_id ON BuddyIntels_linked_topics(linked_by_user_id);
CREATE INDEX IF NOT EXISTS idx_topic_permissions_user_id ON BuddyIntels_topic_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_processed_messages_chat_id ON BuddyIntels_processed_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_processed_messages_user_id ON BuddyIntels_processed_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_tweet_summaries_tweet_id ON BuddyIntels_tweet_summaries(tweet_id);
CREATE INDEX IF NOT EXISTS idx_tweet_summaries_expires_at ON BuddyIntels_tweet_summaries(expires_at);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON BuddyIntels_error_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_topic_activity_logs_topic_id ON BuddyIntels_topic_activity_logs(topic_id);
CREATE INDEX IF NOT EXISTS idx_topic_activity_logs_user_id ON BuddyIntels_topic_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_topic_activity_logs_created_at ON BuddyIntels_topic_activity_logs(created_at);

-- Insert initial configuration
INSERT INTO BuddyIntels_bot_config (key, value) VALUES
  ('rate_limit_per_minute', '5'),
  ('summary_cache_hours', '24'),
  ('max_context_depth', '5'),
  ('require_admin_for_linking', 'false'),
  ('auto_detect_topic_names', 'true')
ON CONFLICT (key) DO NOTHING;

-- Create a function to clean up expired summaries
CREATE OR REPLACE FUNCTION cleanup_expired_summaries()
RETURNS void AS $$
BEGIN
  DELETE FROM BuddyIntels_tweet_summaries WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a function to log errors
CREATE OR REPLACE FUNCTION log_error(
  error_type_param TEXT,
  error_message_param TEXT,
  stack_trace_param TEXT DEFAULT NULL,
  context_param JSONB DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  INSERT INTO BuddyIntels_error_logs (error_type, error_message, stack_trace, context)
  VALUES (error_type_param, error_message_param, stack_trace_param, context_param);
END;
$$ LANGUAGE plpgsql;

-- Create a function to log topic activity
CREATE OR REPLACE FUNCTION log_topic_activity(
  topic_id_param INTEGER,
  user_id_param BIGINT,
  username_param TEXT,
  action_param TEXT,
  details_param JSONB DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  INSERT INTO BuddyIntels_topic_activity_logs (topic_id, user_id, username, action, details)
  VALUES (topic_id_param, user_id_param, username_param, action_param, details_param);
END;
$$ LANGUAGE plpgsql;

-- Create a function to get active linked topics
CREATE OR REPLACE FUNCTION get_active_linked_topics()
RETURNS TABLE (
  id INTEGER,
  group_id BIGINT,
  topic_id BIGINT,
  group_title TEXT,
  topic_title TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    lt.id,
    lt.group_id,
    lt.topic_id,
    lt.group_title,
    lt.topic_title
  FROM BuddyIntels_linked_topics lt
  WHERE lt.is_active = TRUE
  ORDER BY lt.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to check if a user can manage topics
CREATE OR REPLACE FUNCTION can_user_manage_topics(user_id_param BIGINT)
RETURNS BOOLEAN AS $$
DECLARE
  user_permissions RECORD;
BEGIN
  SELECT * INTO user_permissions
  FROM BuddyIntels_topic_permissions
  WHERE user_id = user_id_param;

  -- If no permissions record exists, check if linking is open to all
  IF NOT FOUND THEN
    RETURN (SELECT value::BOOLEAN FROM BuddyIntels_bot_config WHERE key = 'require_admin_for_linking') = FALSE;
  END IF;

  RETURN user_permissions.is_admin OR user_permissions.can_link_topics;
END;
$$ LANGUAGE plpgsql;